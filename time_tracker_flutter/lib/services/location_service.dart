import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_local_notifications/src/platform_specifics/android/enums.dart' as android_notification;
import 'package:latlong2/latlong.dart';
import 'package:time_tracker_flutter/models/location_area.dart';
import 'package:time_tracker_flutter/models/project.dart';
import 'package:time_tracker_flutter/services/database_service.dart';
import 'package:time_tracker_flutter/services/service_initializer.dart';

import 'location/location_events.dart';
import 'location/location_utils.dart';
import 'location/location_permissions.dart';
import 'location/background_communication.dart';
import 'location_task_handler.dart';

class LocationService {
  static const _notificationId = 8675309;
  static const _notificationChannelId = 'location_tracking';
  static const _notificationChannelName = 'Location Tracking';
  static const _notificationChannelDescription = 'Notifications for location-based time tracking';

  // Singleton
  static final LocationService _instance = LocationService._internal();
  static final _initializer = ServiceInitializer<LocationService>(() => _instance._initialize());

  factory LocationService() => _instance;
  LocationService._internal();

  // Dependencies
  final DatabaseService _databaseService = DatabaseService();
  FlutterLocalNotificationsPlugin? _notificationsPlugin;

  // Event streams
  final _eventStreamController = StreamController<LocationEvent>.broadcast();
  final _statusStreamController = StreamController<TrackingStatus>.broadcast();

  // Background communication
  BackgroundCommunication? _backgroundComm;
  StreamSubscription<Object>? _taskDataSubscription;

  // Public streams
  Stream<LocationEvent> get eventStream => _eventStreamController.stream;
  Stream<TrackingStatus> get statusStream => _statusStreamController.stream;

  // Status getters
  bool get isInitialized => _initializer.isInitialized;
  bool get isTrackingActive => _backgroundComm?.currentStatus.isActive ?? false;
  Set<String> get trackingAreaIds => _backgroundComm?.currentStatus.activeTimeEntries.keys.toSet() ?? {};
  TrackingStatus get currentStatus => _backgroundComm?.currentStatus ?? TrackingStatus(isActive: false, activeTimeEntries: {});

  Future<LocationService> _initialize() async {
    if (!_shouldInitializeNotifications) return this;

    // Initialize background communication
    _backgroundComm = BackgroundCommunication(_eventStreamController, _statusStreamController);
    _backgroundComm!.initialize();

    await _initializeForegroundTask();

    _notificationsPlugin = FlutterLocalNotificationsPlugin();
    await _initializeNotifications();

    await _restoreActiveState();

    return this;
  }

  Future<void> _initializeForegroundTask() async {
    FlutterForegroundTask.init(
      androidNotificationOptions: AndroidNotificationOptions(
        channelId: _notificationChannelId,
        channelName: _notificationChannelName,
        channelDescription: _notificationChannelDescription,
        onlyAlertOnce: true,
      ),
      iosNotificationOptions: const IOSNotificationOptions(
        showNotification: true,
        playSound: false,
      ),
      foregroundTaskOptions: ForegroundTaskOptions(
        eventAction: ForegroundTaskEventAction.repeat(10000),
        autoRunOnBoot: true,
        autoRunOnMyPackageReplaced: true,
        allowWakeLock: true,
        allowWifiLock: true,
      ),
    );
  }

  bool get _shouldInitializeNotifications =>
      !kIsWeb && (Platform.isAndroid || Platform.isIOS);

  Future<void> _initializeNotifications() async {
    const initializationSettingsAndroid = AndroidInitializationSettings('notification_icon');
    final initializationSettingsIOS = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    final initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _notificationsPlugin!.initialize(initializationSettings);

    if (Platform.isAndroid) {
      await _createNotificationChannel();
    }
  }

  Future<void> _createNotificationChannel() async {
    final channel = AndroidNotificationChannel(
      _notificationChannelId,
      _notificationChannelName,
      description: _notificationChannelDescription,
      importance: Importance.max,
      enableVibration: true,
      enableLights: true,
      playSound: true,
      showBadge: true,
      ledColor: const Color(0xFF4CAF50),
    );

    await _notificationsPlugin!
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
  }

  Future<bool> startTracking([LocationArea? area]) async {
    try {
      debugPrint('Starting location tracking${area != null ? ' for area: ${area.name}' : ''}');

      if (!LocationPermissions.isLocationSupported) {
        debugPrint('Location tracking not supported on this platform');
        return false;
      }

      final hasPermission = await LocationPermissions.requestAllPermissions();
      if (!hasPermission) {
        debugPrint('Required permissions not granted');
        return false;
      }

      final wasActive = currentStatus.isActive;

      if (!wasActive) {
        final serviceStarted = await _startForegroundService();
        if (!serviceStarted) {
          debugPrint('Failed to start foreground service');
          return false;
        }

        // Send initial data to the background task immediately
        await _sendLatestDataToBackground();
      }

      if (area != null) {
        await _enableAreaTracking(area);
      }

      return true;
    } catch (e) {
      debugPrint('Error starting location tracking: $e');
      return false;
    }
  }

  Future<bool> _startForegroundService() async {
    if (currentStatus.isActive) {
      debugPrint('Location tracking is already active');
      return true;
    }

    try {
      final result = await FlutterForegroundTask.startService(
        notificationTitle: 'Location Tracking Active',
        notificationText: 'Waiting to enter a tracking area...',
        callback: startLocationTaskCallback,
      );

      if (result != null) {
        debugPrint('Foreground service started successfully');
        return true;
      } else {
        debugPrint('Failed to start foreground service - result was null');
        return false;
      }
    } catch (e) {
      debugPrint('Error starting foreground service: $e');
      return false;
    }
  }

  Future<void> _enableAreaTracking(LocationArea area) async {
    final updatedArea = area.copyWith(isActive: true);
    await _databaseService.saveLocationArea(updatedArea);

    if (currentStatus.isActive) {
      // Send updated data first to ensure the background task has latest information
      await _sendLatestDataToBackground();

      // Send the tracking command immediately
      FlutterForegroundTask.sendDataToTask({
        'command': 'startTracking',
        'area': jsonEncode(updatedArea.toJson()),
      });

      debugPrint('Sent area tracking command for ${updatedArea.name}');
    } else {
      debugPrint('Warning: Tried to enable area tracking but service is not active');
    }
  }

  Future<void> _sendLatestDataToBackground() async {
    try {
      final projects = await _databaseService.getProjects();
      final areas = await _databaseService.getLocationAreas();

      FlutterForegroundTask.sendDataToTask({
        'command': 'updateData',
        'projects': projects.map((p) => p.toJson()).toList(),
        'areas': areas.map((a) => a.toJson()).toList(),
      });

      debugPrint('Sent ${projects.length} projects and ${areas.length} areas to background task');
    } catch (e) {
      debugPrint('Error sending latest data to background: $e');
    }
  }

  Future<void> updateLocationArea(LocationArea area) async {
    try {
      // Save the updated area to the database
      await _databaseService.saveLocationArea(area);

      // If tracking is active, update the background service with the new area data
      if (currentStatus.isActive) {
        await _sendLatestDataToBackground();

        // Send specific update command for this area
        FlutterForegroundTask.sendDataToTask({
          'command': 'updateArea',
          'area': jsonEncode(area.toJson()),
        });

        debugPrint('Updated location area ${area.name} and notified background service');
      }
    } catch (e) {
      debugPrint('Error updating location area: $e');
      rethrow;
    }
  }

  Future<void> stopTracking([LocationArea? area]) async {
    try {
      debugPrint('Stopping location tracking${area != null ? ' for area: ${area.name}' : ''}');

      if (!currentStatus.isActive) {
        debugPrint('Location tracking is not active');
        return;
      }

      if (area != null) {
        await _stopAreaTracking(area);
      } else {
        await _stopAllTracking();
      }
    } catch (e) {
      debugPrint('Error stopping location tracking: $e');
      rethrow;
    }
  }

  Future<void> _stopAreaTracking(LocationArea area) async {
    await _databaseService.saveLocationArea(area.copyWith(isActive: false));

    if (currentStatus.isActive) {
      FlutterForegroundTask.sendDataToTask({
        'command': 'stopTracking',
        'area': jsonEncode(area.toJson()),
      });
    }

    final areas = await _databaseService.getLocationAreas();
    final activeAreas = areas.where((a) => a.isActive);
    if (activeAreas.isEmpty) {
      await _stopForegroundService();
    }
  }

  Future<void> _stopAllTracking() async {
    final areas = await _databaseService.getLocationAreas();
    for (final area in areas) {
      if (area.isActive) {
        await _databaseService.saveLocationArea(area.copyWith(isActive: false));
      }
    }

    if (currentStatus.isActive) {
      FlutterForegroundTask.sendDataToTask({
        'command': 'stopTracking',
      });
    }

    await _stopForegroundService();
  }

  Future<void> _stopForegroundService() async {
    try {
      await _taskDataSubscription?.cancel();
      _taskDataSubscription = null;
      await FlutterForegroundTask.stopService();
    } catch (e) {
      debugPrint('Error stopping foreground service: $e');
    }
  }

  Future<bool> isProjectBeingTracked(String projectId) async {
    if (!currentStatus.isActive || currentStatus.activeTimeEntries.isEmpty) {
      return false;
    }

    try {
      final areas = await _databaseService.getLocationAreas();

      for (final areaId in currentStatus.activeTimeEntries.keys) {
        final area = areas.firstWhere(
          (a) => a.id == areaId,
          orElse: () => throw StateError('Area not found'),
        );

        if (area.projectId == projectId) {
          return true;
        }
      }

      return false;
    } catch (e) {
      debugPrint('Error checking if project is being tracked: $e');
      return false;
    }
  }

  Future<Map<String, dynamic>?> getActiveTrackingInfo(String projectId) async {
    if (!currentStatus.isActive || currentStatus.activeTimeEntries.isEmpty) {
      return null;
    }

    try {
      final areas = await _databaseService.getLocationAreas();

      for (final entry in currentStatus.activeTimeEntries.entries) {
        try {
          final area = areas.firstWhere(
            (a) => a.id == entry.key && a.projectId == projectId,
            orElse: () => throw StateError('Area not found'),
          );

          final timeEntry = await _databaseService.getTimeEntry(entry.value);
          if (timeEntry?.inProgress ?? false) {
            return {
              'locationName': area.name,
              'startTime': LocationUtils.parseDateTime(timeEntry!.date, timeEntry.start),
              'timeEntryId': entry.value,
              'areaId': area.id,
            };
          }
        } catch (e) {
          debugPrint('Error processing area entry: $e');
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error getting active tracking info: $e');
      return null;
    }
  }

  String formatTrackingDuration(DateTime startTime) {
    return LocationUtils.formatTrackingDuration(startTime);
  }

  Future<void> _restoreActiveState() async {
    try {
      debugPrint('Checking for existing active time entries');
      final areas = await _databaseService.getLocationAreas();
      final activeAreas = areas.where((area) => area.isActive);

      if (activeAreas.isNotEmpty) {
        debugPrint('Found ${activeAreas.length} active areas, available for tracking');
      }
    } catch (e) {
      debugPrint('Error restoring active state: $e');
    }
  }

  void dispose() {
    _taskDataSubscription?.cancel();
    _backgroundComm.dispose();
    _eventStreamController.close();
    _statusStreamController.close();
  }

  static Future<void> initializeService() async => await _initializer.service;
}